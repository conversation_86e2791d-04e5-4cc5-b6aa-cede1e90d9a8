"""
数据增强模块 - 用于信号数据的增强处理
"""

import torch
import torch.nn as nn
import numpy as np
import random


class SignalAugmentation(nn.Module):
    """
    信号数据增强类，专门用于无线信号调制识别任务
    """
    
    def __init__(self, 
                 noise_std=0.05,           # 噪声标准差
                 freq_shift_range=0.1,     # 频率偏移范围
                 time_shift_range=0.1,     # 时间偏移范围
                 amplitude_scale_range=(0.8, 1.2),  # 幅度缩放范围
                 phase_shift_range=0.2,    # 相位偏移范围
                 dropout_prob=0.1,         # 时域dropout概率
                 augment_prob=0.5):        # 应用增强的概率
        """
        初始化数据增强器
        
        Args:
            noise_std: 添加高斯噪声的标准差
            freq_shift_range: 频率偏移的范围 [-range, range]
            time_shift_range: 时间偏移的范围 [0, range]
            amplitude_scale_range: 幅度缩放的范围 (min, max)
            phase_shift_range: 相位偏移的范围 [-range, range]
            dropout_prob: 时域dropout的概率
            augment_prob: 应用增强的概率
        """
        super(SignalAugmentation, self).__init__()
        self.noise_std = noise_std
        self.freq_shift_range = freq_shift_range
        self.time_shift_range = time_shift_range
        self.amplitude_scale_range = amplitude_scale_range
        self.phase_shift_range = phase_shift_range
        self.dropout_prob = dropout_prob
        self.augment_prob = augment_prob
        
    def add_gaussian_noise(self, x):
        """添加高斯噪声"""
        if random.random() < self.augment_prob:
            noise = torch.randn_like(x) * self.noise_std
            x = x + noise
        return x
    
    def amplitude_scaling(self, x):
        """幅度缩放"""
        if random.random() < self.augment_prob:
            scale = random.uniform(*self.amplitude_scale_range)
            x = x * scale
        return x
    
    def phase_shift(self, x):
        """相位偏移 - 对复数信号进行相位旋转"""
        if random.random() < self.augment_prob:
            # 将I/Q转换为复数
            complex_signal = x[:, 0, :] + 1j * x[:, 1, :]
            
            # 生成随机相位偏移
            phase_offset = random.uniform(-self.phase_shift_range, self.phase_shift_range) * np.pi
            phase_rotation = torch.exp(1j * torch.tensor(phase_offset, device=x.device))
            
            # 应用相位偏移
            rotated_signal = complex_signal * phase_rotation
            
            # 转换回I/Q格式
            x[:, 0, :] = rotated_signal.real
            x[:, 1, :] = rotated_signal.imag
            
        return x
    
    def time_shift(self, x):
        """时间偏移 - 循环移位"""
        if random.random() < self.augment_prob:
            seq_len = x.size(-1)
            shift_amount = int(random.uniform(0, self.time_shift_range) * seq_len)
            if shift_amount > 0:
                x = torch.roll(x, shift_amount, dims=-1)
        return x
    
    def frequency_shift(self, x):
        """频率偏移 - 通过复数乘法实现"""
        if random.random() < self.augment_prob:
            seq_len = x.size(-1)
            freq_offset = random.uniform(-self.freq_shift_range, self.freq_shift_range)
            
            # 生成频率偏移的复数指数
            t = torch.arange(seq_len, device=x.device, dtype=torch.float32)
            freq_shift_complex = torch.exp(1j * 2 * np.pi * freq_offset * t / seq_len)
            
            # 将I/Q转换为复数
            complex_signal = x[:, 0, :] + 1j * x[:, 1, :]
            
            # 应用频率偏移
            shifted_signal = complex_signal * freq_shift_complex
            
            # 转换回I/Q格式
            x[:, 0, :] = shifted_signal.real
            x[:, 1, :] = shifted_signal.imag
            
        return x
    
    def time_dropout(self, x):
        """时域dropout - 随机将某些时间点设为0"""
        if random.random() < self.augment_prob:
            seq_len = x.size(-1)
            dropout_mask = torch.rand(seq_len, device=x.device) > self.dropout_prob
            x = x * dropout_mask.unsqueeze(0).unsqueeze(0)
        return x
    
    def mixup(self, x, y, alpha=0.2):
        """Mixup数据增强 - 混合两个样本"""
        if random.random() < self.augment_prob and x.size(0) > 1:
            batch_size = x.size(0)
            indices = torch.randperm(batch_size, device=x.device)
            
            # 生成混合系数
            lam = np.random.beta(alpha, alpha)
            
            # 混合输入
            mixed_x = lam * x + (1 - lam) * x[indices]
            
            # 混合标签（返回原标签和混合信息，在损失计算时处理）
            mixed_y = (y, y[indices], lam)
            
            return mixed_x, mixed_y
        
        return x, y
    
    def forward(self, x):
        """
        前向传播 - 应用数据增强
        
        Args:
            x: 输入信号 [B, 2, L]
            
        Returns:
            增强后的信号 [B, 2, L]
        """
        # 只在训练模式下应用增强
        if not self.training:
            return x
            
        # 应用各种增强技术
        x = self.add_gaussian_noise(x)
        x = self.amplitude_scaling(x)
        x = self.phase_shift(x)
        x = self.time_shift(x)
        x = self.frequency_shift(x)
        x = self.time_dropout(x)
        
        return x


def mixup_criterion(criterion, pred, y_mixed, alpha=0.2):
    """
    Mixup损失函数
    
    Args:
        criterion: 原始损失函数
        pred: 模型预测 [B, num_classes]
        y_mixed: 混合标签信息 (y_a, y_b, lam)
        alpha: mixup参数
        
    Returns:
        混合损失
    """
    if isinstance(y_mixed, tuple):
        y_a, y_b, lam = y_mixed
        return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)
    else:
        return criterion(pred, y_mixed)


class AugmentedDataset(torch.utils.data.Dataset):
    """
    增强数据集包装器
    """
    
    def __init__(self, base_dataset, augmentation=None, mixup_alpha=0.0):
        """
        初始化增强数据集
        
        Args:
            base_dataset: 基础数据集
            augmentation: 数据增强器
            mixup_alpha: Mixup参数，0表示不使用mixup
        """
        self.base_dataset = base_dataset
        self.augmentation = augmentation
        self.mixup_alpha = mixup_alpha
        
    def __len__(self):
        return len(self.base_dataset)
    
    def __getitem__(self, idx):
        x, y, snr = self.base_dataset[idx]
        
        # 应用数据增强
        if self.augmentation is not None:
            x = self.augmentation(x.unsqueeze(0)).squeeze(0)
            
        return x, y, snr


def create_augmentation(config):
    """
    根据配置创建数据增强器
    
    Args:
        config: 配置字典
        
    Returns:
        数据增强器实例
    """
    aug_config = config.get('augmentation', {})
    
    if not aug_config.get('enabled', False):
        return None
        
    return SignalAugmentation(
        noise_std=aug_config.get('noise_std', 0.05),
        freq_shift_range=aug_config.get('freq_shift_range', 0.1),
        time_shift_range=aug_config.get('time_shift_range', 0.1),
        amplitude_scale_range=tuple(aug_config.get('amplitude_scale_range', [0.8, 1.2])),
        phase_shift_range=aug_config.get('phase_shift_range', 0.2),
        dropout_prob=aug_config.get('dropout_prob', 0.1),
        augment_prob=aug_config.get('augment_prob', 0.5)
    )
